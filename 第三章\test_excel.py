import pandas as pd

# 测试读取Excel文件
try:
    print("正在测试读取Excel文件...")
    
    # 读取所有sheet
    excel_file = pd.ExcelFile("实验人口统计数据.xlsx")
    print("可用的sheet名称:", excel_file.sheet_names)
    
    # 读取A组数据
    df_A = pd.read_excel("实验人口统计数据.xlsx", sheet_name='A')
    print(f"\nA组数据形状: {df_A.shape}")
    print("A组列名:", df_A.columns.tolist())
    print("A组前5行:")
    print(df_A.head())
    
    # 读取B组数据
    df_B = pd.read_excel("实验人口统计数据.xlsx", sheet_name='B')
    print(f"\nB组数据形状: {df_B.shape}")
    print("B组列名:", df_B.columns.tolist())
    print("B组前5行:")
    print(df_B.head())
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
