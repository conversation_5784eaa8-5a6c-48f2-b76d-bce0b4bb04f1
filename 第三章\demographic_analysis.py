#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人口统计数据分析脚本
分析实验人口统计数据Excel文件中A组和B组的统计参数
"""

import pandas as pd
import numpy as np
import os
import sys

def main():
    """主函数"""
    print("=" * 60)
    print("人口统计数据分析")
    print("=" * 60)
    
    # 文件路径
    file_path = "实验人口统计数据.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件 '{file_path}' 不存在")
        print(f"当前工作目录: {os.getcwd()}")
        print("目录中的文件:")
        for f in os.listdir('.'):
            print(f"  {f}")
        return
    
    try:
        # 读取Excel文件
        print(f"正在读取文件: {file_path}")
        
        # 先检查所有可用的sheet
        excel_file = pd.ExcelFile(file_path)
        print(f"可用的sheet: {excel_file.sheet_names}")
        
        # 读取A组和B组数据
        if 'A' not in excel_file.sheet_names:
            print("错误: 找不到名为 'A' 的sheet")
            return
        if 'B' not in excel_file.sheet_names:
            print("错误: 找不到名为 'B' 的sheet")
            return
            
        df_A = pd.read_excel(file_path, sheet_name='A')
        df_B = pd.read_excel(file_path, sheet_name='B')
        
        print(f"A组数据: {df_A.shape[0]}行 x {df_A.shape[1]}列")
        print(f"B组数据: {df_B.shape[0]}行 x {df_B.shape[1]}列")
        
        # 分析每组数据
        results = {}
        for group_name, df in [('A', df_A), ('B', df_B)]:
            print(f"\n{'='*50}")
            print(f"分析 {group_name} 组数据")
            print(f"{'='*50}")
            
            result = analyze_group(df, group_name)
            if result:
                results[group_name] = result
        
        # 生成汇总报告
        if results:
            generate_summary_report(results)
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def analyze_group(df, group_name):
    """分析单个组的数据"""
    
    print(f"原始数据列名: {df.columns.tolist()}")
    print(f"数据前5行:")
    print(df.head())
    
    # 查找需要的列
    required_columns = {
        'gender': None,  # 性别列
        'age': None,     # 年龄列
        'height': None,  # 身高列
        'weight': None   # 体重列
    }
    
    # 尝试匹配列名
    for col in df.columns:
        col_str = str(col).strip()
        if col_str == 'G' or '性别' in col_str:
            required_columns['gender'] = col
        elif '年龄' in col_str or 'age' in col_str.lower():
            required_columns['age'] = col
        elif '身高' in col_str or 'height' in col_str.lower():
            required_columns['height'] = col
        elif '体重' in col_str or 'weight' in col_str.lower():
            required_columns['weight'] = col
    
    print(f"识别的列映射: {required_columns}")
    
    # 检查是否找到所有必需的列
    missing_cols = [k for k, v in required_columns.items() if v is None]
    if missing_cols:
        print(f"错误: 找不到以下列: {missing_cols}")
        return None
    
    # 提取需要的数据
    analysis_data = df[[
        required_columns['gender'],
        required_columns['age'],
        required_columns['height'],
        required_columns['weight']
    ]].copy()
    
    # 重命名列以便处理
    analysis_data.columns = ['性别', '年龄', '身高', '体重']
    
    # 清理数据
    print(f"清理前数据行数: {len(analysis_data)}")
    analysis_data = analysis_data.dropna()
    print(f"清理后数据行数: {len(analysis_data)}")
    
    if len(analysis_data) == 0:
        print("警告: 没有有效数据")
        return None
    
    # 检查性别编码
    gender_values = analysis_data['性别'].unique()
    print(f"性别编码值: {gender_values}")
    
    # 计算统计量
    results = {}
    
    # 整体统计
    print(f"\n{group_name}组整体统计:")
    print("-" * 30)
    overall_stats = {}
    for param in ['年龄', '身高', '体重']:
        mean_val = analysis_data[param].mean()
        std_val = analysis_data[param].std()
        overall_stats[param] = {'mean': mean_val, 'std': std_val}
        print(f"{param}: 均值={mean_val:.2f}, 标准差={std_val:.2f}")
    
    results['overall'] = overall_stats
    results['total_n'] = len(analysis_data)
    
    # 按性别分组统计
    print(f"\n{group_name}组按性别统计:")
    print("-" * 30)
    
    # 男性 (假设0代表男性)
    male_data = analysis_data[analysis_data['性别'] == 0]
    print(f"男性样本数: {len(male_data)}")
    if len(male_data) > 0:
        male_stats = {}
        for param in ['年龄', '身高', '体重']:
            mean_val = male_data[param].mean()
            std_val = male_data[param].std() if len(male_data) > 1 else 0
            male_stats[param] = {'mean': mean_val, 'std': std_val}
            print(f"  男性{param}: 均值={mean_val:.2f}, 标准差={std_val:.2f}")
        results['male'] = male_stats
        results['male_n'] = len(male_data)
    else:
        results['male'] = {}
        results['male_n'] = 0
    
    # 女性 (假设1代表女性)
    female_data = analysis_data[analysis_data['性别'] == 1]
    print(f"女性样本数: {len(female_data)}")
    if len(female_data) > 0:
        female_stats = {}
        for param in ['年龄', '身高', '体重']:
            mean_val = female_data[param].mean()
            std_val = female_data[param].std() if len(female_data) > 1 else 0
            female_stats[param] = {'mean': mean_val, 'std': std_val}
            print(f"  女性{param}: 均值={mean_val:.2f}, 标准差={std_val:.2f}")
        results['female'] = female_stats
        results['female_n'] = len(female_data)
    else:
        results['female'] = {}
        results['female_n'] = 0
    
    return results

def generate_summary_report(results):
    """生成汇总报告"""
    print(f"\n{'='*80}")
    print("汇总统计报告")
    print(f"{'='*80}")
    
    # 创建汇总表格数据
    summary_data = []
    
    for group in ['A', 'B']:
        if group not in results:
            continue
            
        group_results = results[group]
        
        # 整体统计
        if 'overall' in group_results:
            for param in ['年龄', '身高', '体重']:
                if param in group_results['overall']:
                    summary_data.append({
                        '组别': f'{group}组',
                        '性别': '整体',
                        '参数': param,
                        '样本量': group_results['total_n'],
                        '均值': f"{group_results['overall'][param]['mean']:.2f}",
                        '标准差': f"{group_results['overall'][param]['std']:.2f}"
                    })
        
        # 男性统计
        if 'male' in group_results and group_results['male_n'] > 0:
            for param in ['年龄', '身高', '体重']:
                if param in group_results['male']:
                    summary_data.append({
                        '组别': f'{group}组',
                        '性别': '男性',
                        '参数': param,
                        '样本量': group_results['male_n'],
                        '均值': f"{group_results['male'][param]['mean']:.2f}",
                        '标准差': f"{group_results['male'][param]['std']:.2f}"
                    })
        
        # 女性统计
        if 'female' in group_results and group_results['female_n'] > 0:
            for param in ['年龄', '身高', '体重']:
                if param in group_results['female']:
                    summary_data.append({
                        '组别': f'{group}组',
                        '性别': '女性',
                        '参数': param,
                        '样本量': group_results['female_n'],
                        '均值': f"{group_results['female'][param]['mean']:.2f}",
                        '标准差': f"{group_results['female'][param]['std']:.2f}"
                    })
    
    # 创建并显示汇总表格
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False))
        
        # 保存结果
        try:
            output_excel = "人口统计分析结果.xlsx"
            summary_df.to_excel(output_excel, index=False)
            print(f"\n结果已保存到: {output_excel}")
            
            output_csv = "人口统计分析结果.csv"
            summary_df.to_csv(output_csv, index=False, encoding='utf-8-sig')
            print(f"结果也已保存为CSV: {output_csv}")
            
        except Exception as e:
            print(f"保存文件时出错: {e}")
    else:
        print("没有生成汇总数据")

if __name__ == "__main__":
    main()
