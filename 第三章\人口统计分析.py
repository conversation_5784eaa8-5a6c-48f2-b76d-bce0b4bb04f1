import pandas as pd
import numpy as np
import os

def analyze_demographic_data():
    """
    分析实验人口统计数据
    读取Excel文件中的A、B两个sheet，计算各组的统计参数
    """
    
    # 文件路径
    file_path = "实验人口统计数据.xlsx"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return
    
    try:
        # 读取A组和B组数据
        df_A = pd.read_excel(file_path, sheet_name='A')
        df_B = pd.read_excel(file_path, sheet_name='B')
        
        print("数据读取成功！")
        print(f"A组数据形状: {df_A.shape}")
        print(f"B组数据形状: {df_B.shape}")
        
        # 显示列名以确认数据结构
        print("\nA组列名:", df_A.columns.tolist())
        print("B组列名:", df_B.columns.tolist())
        
        # 分析函数
        def calculate_stats(df, group_name):
            """计算统计参数"""
            print(f"\n{'='*50}")
            print(f"{group_name}组统计分析")
            print(f"{'='*50}")
            
            # 检查必要的列是否存在
            required_cols = ['G', '年龄', '身高', '体重']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"缺少列: {missing_cols}")
                return
            
            # 清理数据，移除缺失值
            df_clean = df[required_cols].dropna()
            print(f"有效数据行数: {len(df_clean)}")
            
            # 整体统计
            print(f"\n{group_name}组整体统计:")
            print("-" * 30)
            
            stats_overall = {}
            for col in ['年龄', '身高', '体重']:
                mean_val = df_clean[col].mean()
                std_val = df_clean[col].std()
                stats_overall[col] = {'mean': mean_val, 'std': std_val}
                print(f"{col}: 均值 = {mean_val:.2f}, 标准差 = {std_val:.2f}")
            
            # 按性别分组统计
            print(f"\n{group_name}组按性别分组统计:")
            print("-" * 30)
            
            # 男性 (G=0)
            df_male = df_clean[df_clean['G'] == 0]
            print(f"\n男性 (n={len(df_male)}):")
            stats_male = {}
            for col in ['年龄', '身高', '体重']:
                if len(df_male) > 0:
                    mean_val = df_male[col].mean()
                    std_val = df_male[col].std()
                    stats_male[col] = {'mean': mean_val, 'std': std_val}
                    print(f"  {col}: 均值 = {mean_val:.2f}, 标准差 = {std_val:.2f}")
                else:
                    print(f"  {col}: 无数据")
            
            # 女性 (G=1)
            df_female = df_clean[df_clean['G'] == 1]
            print(f"\n女性 (n={len(df_female)}):")
            stats_female = {}
            for col in ['年龄', '身高', '体重']:
                if len(df_female) > 0:
                    mean_val = df_female[col].mean()
                    std_val = df_female[col].std()
                    stats_female[col] = {'mean': mean_val, 'std': std_val}
                    print(f"  {col}: 均值 = {mean_val:.2f}, 标准差 = {std_val:.2f}")
                else:
                    print(f"  {col}: 无数据")
            
            return {
                'overall': stats_overall,
                'male': stats_male,
                'female': stats_female,
                'sample_sizes': {
                    'total': len(df_clean),
                    'male': len(df_male),
                    'female': len(df_female)
                }
            }
        
        # 分析A组和B组
        stats_A = calculate_stats(df_A, 'A')
        stats_B = calculate_stats(df_B, 'B')
        
        # 生成汇总表格
        print(f"\n{'='*80}")
        print("汇总统计表")
        print(f"{'='*80}")
        
        # 创建汇总DataFrame
        summary_data = []
        
        for group_name, stats in [('A组', stats_A), ('B组', stats_B)]:
            if stats:
                # 整体统计
                for param in ['年龄', '身高', '体重']:
                    if param in stats['overall']:
                        summary_data.append({
                            '组别': group_name,
                            '性别': '整体',
                            '参数': param,
                            '样本量': stats['sample_sizes']['total'],
                            '均值': f"{stats['overall'][param]['mean']:.2f}",
                            '标准差': f"{stats['overall'][param]['std']:.2f}"
                        })
                
                # 男性统计
                for param in ['年龄', '身高', '体重']:
                    if param in stats['male']:
                        summary_data.append({
                            '组别': group_name,
                            '性别': '男性',
                            '参数': param,
                            '样本量': stats['sample_sizes']['male'],
                            '均值': f"{stats['male'][param]['mean']:.2f}",
                            '标准差': f"{stats['male'][param]['std']:.2f}"
                        })
                
                # 女性统计
                for param in ['年龄', '身高', '体重']:
                    if param in stats['female']:
                        summary_data.append({
                            '组别': group_name,
                            '性别': '女性',
                            '参数': param,
                            '样本量': stats['sample_sizes']['female'],
                            '均值': f"{stats['female'][param]['mean']:.2f}",
                            '标准差': f"{stats['female'][param]['std']:.2f}"
                        })
        
        # 显示汇总表格
        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            print(summary_df.to_string(index=False))
            
            # 保存汇总结果到Excel
            output_file = "人口统计分析结果.xlsx"
            summary_df.to_excel(output_file, index=False)
            print(f"\n汇总结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_demographic_data()
